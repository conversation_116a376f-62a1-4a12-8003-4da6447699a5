<template>
  <div ref="wrapRef" class="g6-wrap"></div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch } from "vue";
import * as G6 from "@antv/g6";

const props = defineProps({
  nodes: { type: Array, required: true }, // [{id, nodeName, label, entityId, attrs}]
  links: { type: Array, required: true }, // [{id, name, source, target}]
  r: { type: Number, default: 16 },
  force: {
    type: Object,
    default: () => ({
      linkDistance: 120,
      nodeStrength: -350,
      edgeStrength: 0.2,
    }),
  },
});

const wrapRef = ref(null);
let graph = null;
let tooltip = null;
let minimap = null;

// 更鲜艳的颜色映射（按 label）
const colorByLabel = (label) => {
  const palette = [
    "#1890ff", // 蓝色
    "#52c41a", // 绿色
    "#faad14", // 橙色
    "#f5222d", // 红色
    "#722ed1", // 紫色
    "#13c2c2", // 青色
    "#eb2f96", // 粉色
    "#fa8c16", // 橙红色
  ];
  const idx = Math.abs((label || "").split("").reduce((s, c) => s + c.charCodeAt(0), 0)) % palette.length;
  return palette[idx];
};

// 把 props 数据转成 G6 的数据结构
const toG6Data = () => {
  const nodes = props.nodes.map((n) => ({
    id: String(n.id),
    // G6 显示 label
    label: n.nodeName || n.id,
    nodeName: n.nodeName,
    entityId: n.entityId,
    rawLabel: n.label, // 原始“类型”
    attrs: n.attrs || [],
    size: props.r * 2.5, // 增大节点尺寸
    style: {
      fill: colorByLabel(n.label),
      stroke: "#fff",
      lineWidth: 3, // 增加边框宽度
      shadowColor: "rgba(0,0,0,0.1)",
      shadowBlur: 6,
    },
    labelCfg: {
      position: "bottom",
      offset: 10,
      style: {
        fill: "#333",
        fontSize: 14, // 增大字体
        fontWeight: "bold",
      },
    },
  }));

  const edges = props.links.map((l) => ({
    id: String(l.id),
    source: String(l.source),
    target: String(l.target),
    label: l.name || "",
    type: "line", // 直线
    style: {
      stroke: "#1890ff", // 更鲜艳的蓝色
      lineWidth: 3, // 增加线宽
      opacity: 0.8,
      endArrow: {
        // 直线箭头
        path: G6.Arrow.triangle(12, 15, 0), // 增大箭头
        d: 15,
        fill: "#1890ff",
      },
    },
    labelCfg: {
      autoRotate: true,
      refY: -8,
      style: {
        fill: "#1890ff",
        fontSize: 13,
        fontWeight: "bold",
        opacity: 0.9,
        background: {
          fill: "#fff",
          stroke: "#1890ff",
          padding: [2, 4, 2, 4],
          radius: 3,
        },
      },
    },
  }));

  return { nodes, edges };
};

function build() {
  const { clientWidth, clientHeight } = wrapRef.value;

  // Tooltip 插件
  tooltip = new G6.Tooltip({
    offsetX: 12,
    offsetY: 12,
    fixToNode: [1, 0], // 贴近节点右侧
    itemTypes: ["node"],
    getContent: (e) => {
      const model = e?.item?.getModel?.() || {};
      const el = document.createElement("div");
      el.className = "g6-tt";
      const type = model.rawLabel ? `<div class="sub">类型：${model.rawLabel}</div>` : "";
      const eid = model.entityId ? `<div class="sub">ID：${model.entityId}</div>` : "";
      el.innerHTML = `<div class="name">${model.nodeName || model.id}</div>${eid}${type}`;
      return el;
    },
  });

  // 小地图（可选）
  minimap = new G6.Minimap({
    size: [160, 110],
    type: "keyShape",
  });

  graph = new G6.Graph({
    container: wrapRef.value,
    width: Math.max(300, clientWidth),
    height: Math.max(240, clientHeight),
    plugins: [tooltip, minimap],
    // 交互
    modes: {
      default: ["drag-canvas", "zoom-canvas", "drag-node"],
    },
    // 全局默认样式
    defaultNode: {
      type: "circle",
      style: {
        cursor: "pointer",
        shadowColor: "rgba(0,0,0,0.1)",
        shadowBlur: 6,
      },
      labelCfg: {
        style: {
          cursor: "default",
          fontWeight: "bold",
        },
      },
    },
    defaultEdge: {
      type: "line",
      style: {
        stroke: "#1890ff",
        lineWidth: 3,
        lineAppendWidth: 8, // 增加鼠标交互区域
        opacity: 0.8,
        endArrow: {
          path: G6.Arrow.triangle(12, 15, 0),
          d: 15,
          fill: "#1890ff",
        },
      },
    },
    // 状态样式（高亮 / 变暗）
    nodeStateStyles: {
      highlight: {
        stroke: "#ff4d4f",
        lineWidth: 4,
        shadowColor: "rgba(255, 77, 79, 0.3)",
        shadowBlur: 10,
      },
      dark: { opacity: 0.3 },
    },
    edgeStateStyles: {
      highlight: {
        stroke: "#ff4d4f",
        lineWidth: 4,
        opacity: 1,
        shadowColor: "rgba(255, 77, 79, 0.2)",
        shadowBlur: 8,
      },
      dark: { opacity: 0.2 },
    },
    layout: {
      type: "force",
      preventOverlap: true,
      nodeSize: props.r * 3, // 增大节点间距
      linkDistance: props.force.linkDistance * 1.5, // 增加连线距离
      nodeStrength: props.force.nodeStrength * 0.8, // 减少节点斥力
      edgeStrength: props.force.edgeStrength * 1.2, // 增加边的吸引力
      damping: 0.9, // 增加阻尼，使布局更稳定
      alpha: 0.3, // 降低初始能量
      alphaDecay: 0.028, // 调整能量衰减
    },
    animate: false,
    fitCenter: true,
  });

  graph.on("afterrender", () => graph.fitView(20));

  // 点击高亮“自己 + 邻居 + 相关边”
  graph.on("node:click", (ev) => {
    const item = ev.item;
    const id = item.getID();
    highlightNeighbors(id);
  });
  // 点击空白取消高亮
  graph.on("canvas:click", () => clearHighlight());

  graph.data(toG6Data());
  graph.render();

  // 监听容器尺寸变化
  const ro = new ResizeObserver(() => {
    if (!graph || graph.get("destroyed")) return;
    const { clientWidth: w, clientHeight: h } = wrapRef.value;
    graph.changeSize(Math.max(300, w), Math.max(240, h));
  });
  ro.observe(wrapRef.value);
  graph.__ro__ = ro;
}

function clearHighlight() {
  if (!graph) return;
  graph.setAutoPaint(false);
  graph.getNodes().forEach((n) => graph.clearItemStates(n));
  graph.getEdges().forEach((e) => graph.clearItemStates(e));
  graph.paint();
  graph.setAutoPaint(true);
}

function highlightNeighbors(centerId) {
  if (!graph) return;
  clearHighlight();

  const center = graph.findById(centerId);
  if (!center) return;

  const neighborNodes = graph.getNeighbors(center, "both");
  const relatedEdges = graph.getEdges().filter((e) => {
    const m = e.getModel();
    return m.source === centerId || m.target === centerId;
  });

  // 先整体“变暗”
  graph.getNodes().forEach((n) => graph.setItemState(n, "dark", true));
  graph.getEdges().forEach((e) => graph.setItemState(e, "dark", true));

  // 再高亮中心、邻居与相关边
  graph.setItemState(center, "dark", false);
  graph.setItemState(center, "highlight", true);

  neighborNodes.forEach((n) => {
    graph.setItemState(n, "dark", false);
  });
  relatedEdges.forEach((e) => {
    graph.setItemState(e, "dark", false);
    graph.setItemState(e, "highlight", true);
  });
}

onMounted(build);

onBeforeUnmount(() => {
  if (graph?.__ro__) graph.__ro__.disconnect?.();
  graph?.destroy?.();
});

// 数据变化：刷新
watch(
  () => [props.nodes, props.links],
  () => {
    if (!graph) return;
    graph.updateLayout({
      linkDistance: props.force.linkDistance,
      nodeStrength: props.force.nodeStrength,
      edgeStrength: props.force.edgeStrength,
    });
    graph.changeData(toG6Data());
    graph.render();
    graph.fitView(20);
  },
  { deep: true }
);
</script>

<style scoped>
.g6-wrap {
  width: 100%;
  height: 100%;
  min-height: 480px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Tooltip 外观 */
.g6-tt {
  background: rgba(20, 24, 36, 0.92);
  color: #fff;
  padding: 8px 10px;
  border-radius: 8px;
  font-size: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.18);
  max-width: 240px;
}
.g6-tt .name {
  font-weight: 600;
  margin-bottom: 2px;
}
.g6-tt .sub {
  opacity: 0.9;
}
</style>
