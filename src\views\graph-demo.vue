<template>
  <div class="demo-container">
    <h1>网络拓扑图优化演示</h1>
    <p>这是优化后的网络拓扑图组件，支持多种布局模式、节点过滤和搜索功能。</p>
    
    <!-- 引入优化后的图表组件 -->
    <GraphView />
  </div>
</template>

<script setup>
import GraphView from './graph.vue'
</script>

<style scoped>
.demo-container {
  padding: 20px;
  background: #f8fafc;
  min-height: 100vh;
}

h1 {
  color: #1e293b;
  margin-bottom: 8px;
}

p {
  color: #64748b;
  margin-bottom: 24px;
}
</style>
