<template>
  <div ref="wrapRef" class="g6-wrap"></div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch } from "vue";
import { Graph } from "@antv/g6";

const props = defineProps({
  nodes: { type: Array, required: true }, // [{id, nodeName, label, entityId, attrs}]
  links: { type: Array, required: true }, // [{id, name, source, target}]
  r: { type: Number, default: 16 },
  force: {
    type: Object,
    default: () => ({
      linkDistance: 120,
      nodeStrength: -350,
      edgeStrength: 0.2,
    }),
  },
});

const wrapRef = ref(null);
let graph = null;

// 简单颜色映射（按 label）
const colorByLabel = (label) => {
  const palette = ["#66c2a5", "#fc8d62", "#8da0cb", "#e78ac3", "#a6d854", "#ffd92f", "#e5c494", "#b3b3b3"];
  const idx = Math.abs((label || "").split("").reduce((s, c) => s + c.charCodeAt(0), 0)) % palette.length;
  return palette[idx];
};

// 把 props 数据转成 G6 v5 的数据结构
const toG6Data = () => {
  const nodes = props.nodes.map((n) => ({
    id: String(n.id),
    data: {
      label: n.nodeName || n.id,
      nodeName: n.nodeName,
      entityId: n.entityId,
      rawLabel: n.label,
      attrs: n.attrs || [],
      fill: colorByLabel(n.label),
    },
  }));

  const edges = props.links.map((l) => ({
    id: String(l.id),
    source: String(l.source),
    target: String(l.target),
    data: {
      label: l.name || "",
    },
  }));

  return { nodes, edges };
};

function build() {
  const { clientWidth, clientHeight } = wrapRef.value;

  graph = new Graph({
    container: wrapRef.value,
    width: Math.max(300, clientWidth),
    height: Math.max(240, clientHeight),
    data: toG6Data(),

    // G6 v5 配置
    node: {
      style: {
        size: props.r * 2,
        fill: (d) => d.data.fill || "#66c2a5",
        stroke: "#fff",
        lineWidth: 2,
      },
      labelText: (d) => d.data.label,
      labelPlacement: "bottom",
      labelOffsetY: 8,
      labelFontSize: 12,
      labelFill: "#333",
    },

    edge: {
      style: {
        stroke: "#8aa4c4",
        lineWidth: 2,
        endArrow: true,
        endArrowSize: 10,
      },
      labelText: (d) => d.data.label,
      labelFontSize: 12,
      labelFill: "#3a70b3",
      labelOffsetY: -6,
    },

    layout: {
      type: "d3-force",
      preventOverlap: true,
      nodeSize: props.r * 2 + 4,
      linkDistance: props.force.linkDistance,
      nodeStrength: props.force.nodeStrength,
      edgeStrength: props.force.edgeStrength,
      damping: 0.8,
    },

    behaviors: ["drag-canvas", "zoom-canvas", "drag-element"],
  });

  // 渲染图表
  graph.render();

  // 自适应视图
  setTimeout(() => {
    graph.fitView();
  }, 100);

  // 监听容器尺寸变化
  const ro = new ResizeObserver(() => {
    if (!graph || graph.destroyed) return;
    const { clientWidth: w, clientHeight: h } = wrapRef.value;
    graph.setSize([Math.max(300, w), Math.max(240, h)]);
  });
  ro.observe(wrapRef.value);
  graph.__ro__ = ro;
}

onMounted(build);

onBeforeUnmount(() => {
  if (graph?.__ro__) graph.__ro__.disconnect?.();
  graph?.destroy?.();
});

// 数据变化：刷新
watch(
  () => [props.nodes, props.links],
  () => {
    if (!graph) return;
    graph.setData(toG6Data());
    setTimeout(() => {
      graph.fitView();
    }, 100);
  },
  { deep: true }
);
</script>

<style scoped>
.g6-wrap {
  width: 100%;
  height: 100%;
  min-height: 480px;
  background: radial-gradient(ellipse at bottom right, rgba(0, 0, 0, 0.04), transparent 60%) no-repeat;
}

/* Tooltip 外观 */
:global(.g6-tooltip) {
  background: rgba(20, 24, 36, 0.92);
  color: #fff;
  padding: 8px 10px;
  border-radius: 8px;
  font-size: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.18);
  max-width: 240px;
}

:global(.g6-tooltip .name) {
  font-weight: 600;
  margin-bottom: 2px;
}

:global(.g6-tooltip .sub) {
  opacity: 0.9;
}
</style>
